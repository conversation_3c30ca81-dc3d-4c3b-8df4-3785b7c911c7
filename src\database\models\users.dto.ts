import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsE<PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsNotEmpty, Matches, MaxLength, IsOptional, IsEnum, IsBoolean, IsObject, IsIn, IsUUID } from 'class-validator';
import { Match } from '../decorators/match.decorator';
import { IsTrue } from '../decorators/is-true.decorator';
import { UserType } from '../entities/user.entity';

export class CreateUserDto {
  @ApiProperty({ example: 'john123', description: 'The name of the user (same as userId)' })
  @IsString()
  @IsNotEmpty({ message: 'Name is required' })
  name: string;

  @ApiProperty({ example: 'john123', description: 'User ID for login' })
  @IsString()
  @IsNotEmpty({ message: 'User ID is required' })
  @MinLength(4, { message: 'User ID must be at least 4 characters long' })
  @MaxLength(20, { message: 'User ID cannot be longer than 20 characters' })
  userId: string;

  @ApiProperty({ example: '<EMAIL>', description: 'User email' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @ApiProperty({ example: '+1234567890', description: 'User phone number' })
  @IsString()
  @IsNotEmpty({ message: 'Phone number is required' })
  @MaxLength(20, { message: 'Phone number cannot be longer than 20 characters' })
  phoneNumber: string;

  @ApiProperty({ example: 'male', description: 'User gender', enum: ['male', 'female', 'N/A'] })
  @IsString()
  @IsNotEmpty({ message: 'Gender is required' })
  @IsIn(['male', 'female', 'N/A'], { message: 'Gender must be male, female, or N/A' })
  gender: string;

  @ApiProperty({ example: 'StrongP@ss123', description: 'User password' })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#].*$/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (@, $, !, %, *, ?, &, or #)',
  })
  password: string;

  @ApiProperty({ example: true, description: 'Agreement to terms and conditions' })
  @IsBoolean()
  @IsNotEmpty({ message: 'You must agree to the terms and conditions' })
  agreedToTerms: boolean;

  @ApiProperty({
    example: 'I have 5 years of experience teaching English.',
    description: 'User bio information',
    required: false,
  })
  @IsString()
  @IsOptional()
  bio?: string;
}
export class UserResponseDto {
  @ApiProperty({ example: '1', description: 'The ID of the user' })
  id: string;

  @ApiProperty({ example: 'John Doe', description: 'The name of the user' })
  name: string;

  @ApiProperty({ example: 'john123', description: 'The user ID for login' })
  userId: string;

  @ApiProperty({ example: '<EMAIL>', description: 'User email' })
  email: string;

  @ApiProperty({ example: 'student', description: 'User type', enum: UserType })
  type: UserType;

  @ApiProperty({ example: true, description: 'Whether the user is active' })
  isActive: boolean;

  @ApiProperty({ example: true, description: 'Whether the user is confirmed' })
  isConfirmed: boolean;

  @ApiProperty({ example: ['Admin'], description: 'Roles assigned to the user' })
  roles: string[];

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Default skin ID for student users (deprecated - use defaultDiarySkinId)', nullable: true })
  defaultSkinId: string | null;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Default diary skin ID for student users', nullable: true })
  defaultDiarySkinId?: string | null;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Default novel skin ID for student users', nullable: true })
  defaultNovelSkinId?: string | null;

  @ApiProperty({ example: 'Premium', description: 'Active subscription plan name', nullable: true })
  activePlan: string | null;

  @ApiProperty({
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      userId: '123e4567-e89b-12d3-a456-************',
      planId: '123e4567-e89b-12d3-a456-************',
      startDate: '2023-01-01T00:00:00.000Z',
      endDate: '2023-02-01T00:00:00.000Z',
      isActive: true,
      isPaid: true,
      autoRenew: true,
      plan: {
        id: '123e4567-e89b-12d3-a456-************',
        name: 'Premium',
        type: 'pro',
        subscriptionType: 'monthly',
        description: 'Premium features',
        price: 29.99,
        features: [
          {
            id: '123e4567-e89b-12d3-a456-************',
            type: 'hec_user_diary',
            name: 'HEC User Diary',
            description: 'Access to the HEC User Diary platform',
          },
        ],
      },
    },
    description: 'Detailed information about the active subscription plan',
    nullable: true,
  })
  activePlanDetails: any | null;

  @ApiProperty({ example: 'student', description: 'Selected role for login', enum: UserType, required: false })
  selectedRole?: UserType;

  @ApiProperty({ example: 'profile-pictures/user123-abc123.jpg', description: 'Profile picture path', required: false })
  profilePicture?: string;

  @ApiProperty({ example: 'http://localhost:3000/users/profile-picture/123e4567-e89b-12d3-a456-************', description: 'Profile picture URL', required: false })
  profilePictureUrl?: string;

  @ApiProperty({ example: '+1234567890', description: 'User phone number', required: false })
  phoneNumber?: string;

  @ApiProperty({ example: '123 Main St', description: 'User address', required: false })
  address?: string;

  @ApiProperty({ example: 'New York', description: 'User city', required: false })
  city?: string;

  @ApiProperty({ example: 'NY', description: 'User state', required: false })
  state?: string;

  @ApiProperty({ example: 'USA', description: 'User country', required: false })
  country?: string;

  @ApiProperty({ example: '10001', description: 'User postal code', required: false })
  postalCode?: string;

  @ApiProperty({ example: 'I am a software developer with 5 years of experience.', description: 'User bio', required: false })
  bio?: string;

  @ApiProperty({ example: '1990-01-01', description: 'User date of birth in YYYY-MM-DD format', required: false })
  dateOfBirth?: string;

  @ApiProperty({ example: 33, description: 'User age calculated from date of birth', required: false })
  age?: number;

  @ApiProperty({ example: 'male', description: 'User gender', required: false })
  gender?: string;

  @ApiProperty({ example: { linkedin: 'https://linkedin.com/in/johndoe', twitter: 'https://twitter.com/johndoe' }, description: 'User social links', required: false })
  socialLinks?: { [key: string]: string };

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'Date when the user last logged in', required: false })
  lastLoginAt?: Date;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'Date when the user was created', required: false })
  createdAt?: Date;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'Date when the user was last updated', required: false })
  updatedAt?: Date;

  @ApiProperty({
    example: [
      {
        id: '123e4567-e89b-12d3-a456-************',
        userId: '123e4567-e89b-12d3-a456-************',
        roleId: '123e4567-e89b-12d3-a456-************',
        roleName: 'admin',
      },
    ],
    description: 'Detailed user roles information',
    required: false,
  })
  userRoles?: any[];

  @ApiProperty({
    example: [
      {
        id: '123e4567-e89b-12d3-a456-************',
        tutorId: '123e4567-e89b-12d3-a456-************',
        tutorName: 'Jane Doe',
        moduleId: '123e4567-e89b-12d3-a456-************',
        moduleName: 'Diary',
      },
    ],
    description: 'Tutors assigned to this student (only for student users)',
    required: false,
  })
  assignedTutors?: any[];

  @ApiProperty({
    example: [
      {
        id: '123e4567-e89b-12d3-a456-************',
        name: 'My Custom Skin',
      },
    ],
    description: 'Skins owned by this student (only for student users)',
    required: false,
  })
  ownedSkins?: any[];

  @ApiProperty({
    example: [
      {
        id: '123e4567-e89b-12d3-a456-************',
        studentId: '123e4567-e89b-12d3-a456-************',
        studentName: 'John Doe',
        moduleId: '123e4567-e89b-12d3-a456-************',
        moduleName: 'Diary',
      },
    ],
    description: 'Students assigned to this tutor (only for tutor users)',
    required: false,
  })
  assignedStudents?: any[];

  @ApiProperty({
    example: [
      {
        id: '123e4567-e89b-12d3-a456-************',
        name: 'Diary',
      },
    ],
    description: 'Modules this tutor is assigned to (only for tutor users)',
    required: false,
  })
  assignedModules?: any[];

  @ApiProperty({
    example: 5,
    description: 'Number of students assigned to this tutor (only for tutor users)',
    required: false,
  })
  assignedStudentCount?: number;

  @ApiProperty({
    example: 3,
    description: 'Number of modules this tutor is assigned to (only for tutor users)',
    required: false,
  })
  assignedModuleCount?: number;

  @ApiProperty({
    example: 2,
    description: 'Number of tutors assigned to this student (only for student users)',
    required: false,
  })
  assignedTutorCount?: number;

  @ApiProperty({
    example: [
      {
        id: '123e4567-e89b-12d3-a456-************',
        tutorId: '123e4567-e89b-12d3-a456-************',
        degree: 'Bachelor of Science',
        institution: 'Harvard University',
        fieldOfStudy: 'Computer Science',
        startDate: '2015-09-01',
        endDate: '2019-06-30',
        isCurrent: false,
        description: 'Studied advanced algorithms and data structures',
        location: 'Cambridge, MA',
        grade: '3.8 GPA',
        activities: 'Member of Computer Science Club, Participated in Hackathons',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
      },
    ],
    description: 'Education information for this tutor (only for tutor users)',
    required: false,
  })
  education?: any[];
}
export class LoginUserDto {
  @ApiProperty({
    example: 'john123',
    description: 'User ID for login (this is your unique identifier, not your display name)',
  })
  @IsString()
  @IsNotEmpty({ message: 'User ID is required' })
  userId: string;

  @ApiProperty({ example: 'StrongP@ss123', description: 'User password' })
  @IsString()
  @IsNotEmpty({ message: 'Password is required' })
  password: string;

  @ApiProperty({ example: 'student', description: 'User role for login (optional - will use highest available role if not specified)', enum: UserType, required: false })
  @IsEnum(UserType, { message: 'Invalid role selected' })
  @IsOptional()
  selectedRole?: UserType;

  @ApiProperty({ example: true, description: 'Remember me for extended login session', required: false })
  @IsBoolean()
  @IsOptional()
  rememberMe?: boolean;

  @ApiProperty({ example: '/dashboard', description: 'URL to redirect to after successful login (defaults to home route if not specified)', required: false })
  @IsString()
  @IsOptional()
  returnUrl?: string;
}

export class RoleSpecificLoginDto {
  @ApiProperty({ example: 'john123', description: 'User ID for login' })
  @IsString()
  @IsNotEmpty({ message: 'User ID is required' })
  userId: string;

  @ApiProperty({ example: 'StrongP@ss123', description: 'User password' })
  @IsString()
  @IsNotEmpty({ message: 'Password is required' })
  password: string;
}

export class UpdateUserDto {
  @ApiProperty({ example: 'John Doe', description: 'The name of the user', required: false })
  @IsString()
  @IsOptional()
  @MinLength(2, { message: 'Name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Name cannot be longer than 100 characters' })
  name?: string;

  @ApiProperty({ example: '<EMAIL>', description: 'User email', required: false })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsOptional()
  email?: string;

  @ApiProperty({ example: 'john123', description: 'User ID for login', required: false })
  @IsString()
  @IsOptional()
  @MinLength(4, { message: 'User ID must be at least 4 characters long' })
  @MaxLength(20, { message: 'User ID cannot be longer than 20 characters' })
  userId?: string;

  @ApiProperty({ example: 'StrongP@ss123', description: 'User password', required: false })
  @IsString()
  @IsOptional()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#].*$/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (@, $, !, %, *, ?, &, or #)',
  })
  password?: string;
}

export class CreateRoleDto {
  @ApiProperty({ example: 'Admin', description: 'Role name' })
  @IsString()
  name: string;
}
export class VerifyEmailDto {
  @ApiProperty({ example: 'abc123def456', description: 'Verification token received in the email link' })
  @IsString()
  @IsNotEmpty({ message: 'Verification token is required' })
  token: string;
}

export class ForgotPasswordDto {
  @ApiProperty({ example: '<EMAIL> or john123', description: 'User email or userId' })
  @IsString()
  @IsNotEmpty({ message: 'Email or userId is required' })
  identifier: string;
}

export class ForgotUserIdDto {
  @ApiProperty({ example: '<EMAIL>', description: 'User email' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;
}

export class ResendVerificationEmailDto {
  @ApiProperty({ example: '<EMAIL>', description: 'User email address used during registration' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;
}

export class RegisterDto {
  @ApiProperty({ example: 'john123', description: 'User ID for login (will also be used as name)' })
  @IsString()
  @IsNotEmpty({ message: 'User ID is required' })
  @MinLength(4, { message: 'User ID must be at least 4 characters long' })
  @MaxLength(20, { message: 'User ID cannot be longer than 20 characters' })
  userId: string;

  @ApiProperty({ example: '<EMAIL>', description: 'User email' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @ApiProperty({ example: '+1234567890', description: 'User phone number (max 20 characters)' })
  @IsString()
  @IsNotEmpty({ message: 'Phone number is required' })
  @MaxLength(20, { message: 'Phone number cannot be longer than 20 characters' })
  phoneNumber: string;

  @ApiProperty({ example: 'male', description: 'User gender', enum: ['male', 'female', 'N/A'] })
  @IsString()
  @IsNotEmpty({ message: 'Gender is required' })
  @IsIn(['male', 'female', 'N/A'], { message: 'Gender must be male, female, or N/A' })
  gender: string;

  @ApiProperty({ example: 'StrongP@ss123', description: 'User password' })
  @IsString()
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#].*$/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (@, $, !, %, *, ?, &, or #)',
  })
  password: string;

  @ApiProperty({ example: 'StrongP@ss123', description: 'Confirm password' })
  @IsString()
  @IsNotEmpty({ message: 'Confirm password is required' })
  @Match('password', { message: 'Passwords do not match' })
  confirmPassword: string;

  @ApiProperty({ example: true, description: 'Agreement to terms and conditions' })
  @IsBoolean()
  @IsNotEmpty({ message: 'You must agree to the terms and conditions' })
  @IsTrue({ message: 'You must agree to the terms and conditions' })
  agreedToTerms: boolean;

  @ApiProperty({
    example: 'student',
    description: 'User type (student or tutor only - admin users can only be created by existing admins)',
    enum: [UserType.STUDENT, UserType.TUTOR],
    default: UserType.STUDENT,
  })
  @IsEnum(UserType, {
    message: 'Invalid user type. Only student and tutor registrations are allowed.',
  })
  @IsOptional()
  type: UserType = UserType.STUDENT;

  @ApiProperty({
    example: 'I have 5 years of experience teaching English.',
    description: 'Bio information (required for tutor registration)',
    required: false,
  })
  @IsString()
  @IsOptional()
  bio?: string;

  toCreateUserDto(): CreateUserDto {
    return {
      name: this.userId, // Use userId as name
      userId: this.userId,
      email: this.email,
      password: this.password,
      phoneNumber: this.phoneNumber,
      gender: this.gender,
      agreedToTerms: this.agreedToTerms,
      bio: this.bio,
    };
  }
}

export class UpdateProfileDto {
  @ApiProperty({
    example: 'John Doe',
    description: 'The display name of the user (for display purposes only, does not affect login credentials)',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MinLength(2, { message: 'Name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Name cannot be longer than 100 characters' })
  name?: string;

  @ApiProperty({ example: '+1234567890', description: 'User phone number', required: false })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty({ example: '123 Main St', description: 'User address', required: false })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({ example: 'New York', description: 'User city', required: false })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiProperty({ example: 'NY', description: 'User state', required: false })
  @IsString()
  @IsOptional()
  state?: string;

  @ApiProperty({ example: 'USA', description: 'User country', required: false })
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty({ example: '10001', description: 'User postal code', required: false })
  @IsString()
  @IsOptional()
  postalCode?: string;

  @ApiProperty({ example: 'I am a software developer with 5 years of experience.', description: 'User bio', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(1000, { message: 'Bio cannot be longer than 1000 characters' })
  bio?: string;

  @ApiProperty({ example: '1990-01-01', description: 'User date of birth in YYYY-MM-DD format', required: false })
  @IsString({ message: 'Date of birth must be a string' })
  @Matches(/^\d{4}-\d{2}-\d{2}$/, { message: 'Date of birth must be in YYYY-MM-DD format (e.g., 1990-01-01)' })
  @IsOptional()
  dateOfBirth?: string;

  @ApiProperty({ example: 'male', description: 'User gender', required: false })
  @IsString()
  @IsOptional()
  gender?: string;

  @ApiProperty({
    example: {
      linkedin: 'https://linkedin.com/in/johndoe',
      twitter: 'https://twitter.com/johndoe',
    },
    description: 'User social links',
    required: false,
  })
  @IsObject()
  @IsOptional()
  socialLinks?: { [key: string]: string };
}

export class ResetPasswordDto {
  @ApiProperty({ example: 'abc123def456', description: 'Reset token received in the reset link' })
  @IsString()
  @IsNotEmpty({ message: 'Reset token is required' })
  token: string;

  @ApiProperty({ example: 'StrongP@ss123', description: 'New password' })
  @IsNotEmpty({ message: 'New password is required' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#].*$/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (@, $, !, %, *, ?, &, or #)',
  })
  newPassword: string;
}

export class RefreshTokenDto {
  @ApiProperty({
    example: 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0',
    description: 'Refresh token received during login with rememberMe enabled'
  })
  @IsString()
  @IsNotEmpty({ message: 'Refresh token is required' })
  refreshToken: string;
}

export class CreateAdminUserDto {
  @ApiProperty({ example: 'admin123', description: 'Admin user ID for login (will also be used as name)' })
  @IsString()
  @IsNotEmpty({ message: 'User ID is required' })
  @MinLength(4, { message: 'User ID must be at least 4 characters long' })
  @MaxLength(20, { message: 'User ID cannot be longer than 20 characters' })
  userId: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Admin email' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @ApiProperty({ example: '+1234567890', description: 'Admin phone number (max 20 characters)' })
  @IsString()
  @IsNotEmpty({ message: 'Phone number is required' })
  @MaxLength(20, { message: 'Phone number cannot be longer than 20 characters' })
  phoneNumber: string;

  @ApiProperty({ example: 'male', description: 'Admin gender', enum: ['male', 'female', 'N/A'] })
  @IsString()
  @IsNotEmpty({ message: 'Gender is required' })
  @IsIn(['male', 'female', 'N/A'], { message: 'Gender must be male, female, or N/A' })
  gender: string;

  @ApiProperty({ example: 'StrongP@ss123', description: 'Admin password' })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#].*$/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (@, $, !, %, *, ?, &, or #)',
  })
  password: string;

  @ApiProperty({ example: 'admin', description: 'User type', enum: UserType, default: UserType.ADMIN })
  @IsEnum(UserType)
  @IsOptional()
  type: UserType = UserType.ADMIN;

  @ApiProperty({ example: true, description: 'Agreement to terms and conditions', default: true })
  @IsBoolean()
  @IsOptional()
  agreedToTerms: boolean = true;
}

export class CreateTutorUserDto {
  @ApiProperty({ example: 'tutor123', description: 'Tutor user ID for login (will also be used as name)' })
  @IsString()
  @IsNotEmpty({ message: 'User ID is required' })
  @MinLength(4, { message: 'User ID must be at least 4 characters long' })
  @MaxLength(20, { message: 'User ID cannot be longer than 20 characters' })
  userId: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Tutor email' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @ApiProperty({ example: '+1234567890', description: 'User phone number (max 20 characters)' })
  @IsString()
  @IsNotEmpty({ message: 'Phone number is required' })
  @MaxLength(20, { message: 'Phone number cannot be longer than 20 characters' })
  phoneNumber: string;

  @ApiProperty({ example: 'male', description: 'User gender', enum: ['male', 'female', 'N/A'] })
  @IsString()
  @IsNotEmpty({ message: 'Gender is required' })
  @IsIn(['male', 'female', 'N/A'], { message: 'Gender must be male, female, or N/A' })
  gender: string;

  @ApiProperty({ example: 'StrongP@ss123', description: 'Tutor password' })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#].*$/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (@, $, !, %, *, ?, &, or #)',
  })
  password: string;

  @ApiProperty({ example: 'tutor', description: 'User type', enum: UserType, default: UserType.TUTOR })
  @IsEnum(UserType)
  @IsOptional()
  type: UserType = UserType.TUTOR;

  @ApiProperty({ example: true, description: 'Agreement to terms and conditions' })
  @IsBoolean()
  @IsOptional()
  agreedToTerms: boolean = false;

  @ApiProperty({
    example: 'I have 5 years of experience teaching English.',
    description: 'Bio information (optional)',
    required: false,
  })
  @IsString()
  @IsOptional()
  bio?: string;

  @ApiProperty({ example: true, description: 'Whether to skip verification (admin only)' })
  @IsBoolean()
  @IsOptional()
  skipVerification?: boolean;
}

export class CalculateAgeDto {
  @ApiProperty({ example: '1990-01-01', description: 'Date of birth in YYYY-MM-DD format' })
  @IsString({ message: 'Date of birth must be a string' })
  @Matches(/^\d{4}-\d{2}-\d{2}$/, { message: 'Date of birth must be in YYYY-MM-DD format (e.g., 1990-01-01)' })
  @IsNotEmpty({ message: 'Date of birth is required' })
  dateOfBirth: string;
}

export class CalculateAgeResponseDto {
  @ApiProperty({ example: 33, description: 'Age calculated from the provided date of birth' })
  age: number;
}

export class CreateStudentUserDto {
  @ApiProperty({ example: 'student123', description: 'Student user ID for login (will also be used as name)' })
  @IsString()
  @IsNotEmpty({ message: 'User ID is required' })
  @MinLength(4, { message: 'User ID must be at least 4 characters long' })
  @MaxLength(20, { message: 'User ID cannot be longer than 20 characters' })
  userId: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Student email' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @ApiProperty({ example: '+1234567890', description: 'User phone number' })
  @IsString()
  @IsNotEmpty({ message: 'Phone number is required' })
  @MaxLength(20, { message: 'Phone number cannot be longer than 20 characters' })
  phoneNumber: string;

  @ApiProperty({ example: 'male', description: 'User gender', enum: ['male', 'female', 'N/A'] })
  @IsString()
  @IsNotEmpty({ message: 'Gender is required' })
  @IsIn(['male', 'female', 'N/A'], { message: 'Gender must be male, female, or N/A' })
  gender: string;

  @ApiProperty({ example: 'StrongP@ss123', description: 'Student password' })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#].*$/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (@, $, !, %, *, ?, &, or #)',
  })
  password: string;

  @ApiProperty({ example: 'student', description: 'User type', enum: UserType, default: UserType.STUDENT })
  @IsEnum(UserType)
  @IsOptional()
  type: UserType = UserType.STUDENT;

  @ApiProperty({ example: true, description: 'Agreement to terms and conditions' })
  @IsBoolean()
  @IsOptional()
  agreedToTerms: boolean = false;

  @ApiProperty({ example: true, description: 'Whether to skip OTP verification (admin only)' })
  @IsBoolean()
  @IsOptional()
  skipVerification?: boolean;
}

export class SetDefaultSkinDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Skin ID to set as default',
  })
  @IsString()
  @IsNotEmpty({ message: 'Skin ID is required' })
  skinId: string;
}

// Simplified DTOs for admin user creation
export class CreateAdminUserByAdminDto {
  @ApiProperty({ example: 'admin123', description: 'Admin user ID for login (will also be used as name)' })
  @IsString()
  @IsNotEmpty({ message: 'User ID is required' })
  @MinLength(4, { message: 'User ID must be at least 4 characters long' })
  @MaxLength(20, { message: 'User ID cannot be longer than 20 characters' })
  userId: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Admin email' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;
}

export class CreateTutorUserByAdminDto {
  @ApiProperty({ example: 'tutor123', description: 'Tutor user ID for login (will also be used as name)' })
  @IsString()
  @IsNotEmpty({ message: 'User ID is required' })
  @MinLength(4, { message: 'User ID must be at least 4 characters long' })
  @MaxLength(20, { message: 'User ID cannot be longer than 20 characters' })
  userId: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Tutor email' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;
}

export class CreateStudentUserByAdminDto {
  @ApiProperty({ example: 'student123', description: 'Student user ID for login (will also be used as name)' })
  @IsString()
  @IsNotEmpty({ message: 'User ID is required' })
  @MinLength(4, { message: 'User ID must be at least 4 characters long' })
  @MaxLength(20, { message: 'User ID cannot be longer than 20 characters' })
  userId: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Student email' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;
}

export class UserCreationResponseDto extends UserResponseDto {
  @ApiProperty({ example: 'TempPass123!', description: 'Generated temporary password' })
  temporaryPassword: string;
}

export class ChangePasswordDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'User ID' })
  @IsString()
  @IsNotEmpty({ message: 'User ID is required' })
  @IsUUID(4, { message: 'User ID must be a valid UUID' })
  userId: string;

  @ApiProperty({ example: 'CurrentP@ss123', description: 'Current password' })
  @IsString()
  @IsNotEmpty({ message: 'Current password is required' })
  currentPassword: string;

  @ApiProperty({ example: 'NewStrongP@ss123', description: 'New password' })
  @IsNotEmpty({ message: 'New password is required' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#].*$/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (@, $, !, %, *, ?, &, or #)',
  })
  newPassword: string;

  @ApiProperty({ example: 'NewStrongP@ss123', description: 'Confirm new password' })
  @IsString()
  @IsNotEmpty({ message: 'Confirm new password is required' })
  confirmNewPassword: string;

  @ApiProperty({ example: true, description: 'Remember me option to extend session duration', default: false })
  @IsOptional()
  @IsBoolean()
  rememberMe?: boolean;
}
