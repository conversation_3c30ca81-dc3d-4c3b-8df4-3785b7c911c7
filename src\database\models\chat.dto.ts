import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID, IsArray, IsBoolean, IsDate, IsNumber } from 'class-validator';
import { ConversationStatus, ConversationType } from '../entities/conversation.entity';
import { MessageStatus, MessageType } from '../entities/message.entity';

/**
 * DTO for conversation participant
 */
export class ConversationParticipantDto {
  @ApiProperty({ description: 'User ID' })
  id: string;

  @ApiProperty({ description: 'User name' })
  name: string;

  @ApiProperty({ description: 'User ID (username)' })
  userId: string;

  @ApiProperty({ description: 'User email' })
  email: string;

  @ApiProperty({ description: 'User type' })
  type: string;

  @ApiProperty({ description: 'User profile picture URL', required: false })
  profilePicture?: string;

  @ApiProperty({ description: 'Is user online', required: false })
  isOnline?: boolean;

  @ApiProperty({ description: 'Last seen at', required: false })
  lastSeenAt?: Date;

  @ApiProperty({ description: 'Unread message count', required: false })
  unreadCount?: number;

  @ApiProperty({ description: 'Conversation ID (if exists)', required: false })
  conversationId?: string;

  @ApiProperty({ description: 'Last message content', required: false })
  lastMessage?: string;

  @ApiProperty({ description: 'Last message time', required: false })
  lastMessageTime?: Date;
}

/**
 * DTO for conversation
 */
export class ConversationDto {
  @ApiProperty({ description: 'Conversation ID' })
  id: string;

  @ApiProperty({ description: 'Conversation type', enum: ConversationType })
  @IsEnum(ConversationType)
  type: ConversationType;

  @ApiProperty({ description: 'Conversation status', enum: ConversationStatus })
  status: ConversationStatus;

  @ApiProperty({ description: 'Conversation participant' })
  participant: ConversationParticipantDto;

  @ApiProperty({ description: 'Last message at', required: false })
  lastMessageAt?: Date;

  @ApiProperty({ description: 'Last message text', required: false })
  lastMessageText?: string;

  @ApiProperty({ description: 'Last message sender ID', required: false })
  lastMessageSenderId?: string;

  @ApiProperty({ description: 'Unread message count' })
  unreadCount: number;

  @ApiProperty({ description: 'Created at' })
  createdAt: Date;

  @ApiProperty({ description: 'Is admin conversation', required: false })
  isAdminConversation?: boolean;
}

/**
 * DTO for message attachment
 */
export class MessageAttachmentDto {
  @ApiProperty({ description: 'Attachment ID' })
  id: string;

  @ApiProperty({ description: 'File path' })
  filePath: string;

  @ApiProperty({ description: 'File name' })
  fileName: string;

  @ApiProperty({ description: 'MIME type' })
  mimeType: string;

  @ApiProperty({ description: 'File size' })
  fileSize: number;

  @ApiProperty({ description: 'Thumbnail path', required: false })
  thumbnailPath?: string;

  @ApiProperty({ description: 'File URL' })
  fileUrl: string;

  @ApiProperty({ description: 'Thumbnail URL', required: false })
  thumbnailUrl?: string;
}

/**
 * DTO for message
 */
export class MessageDto {
  @ApiProperty({ description: 'Message ID' })
  id: string;

  @ApiProperty({ description: 'Conversation ID' })
  conversationId: string;

  @ApiProperty({ description: 'Sender ID' })
  senderId: string;

  @ApiProperty({ description: 'Sender name' })
  senderName: string;

  @ApiProperty({ description: 'Sender profile picture', required: false })
  senderProfilePicture?: string;

  @ApiProperty({ description: 'Recipient ID' })
  recipientId: string;

  @ApiProperty({ description: 'Message type', enum: MessageType })
  type: MessageType;

  @ApiProperty({ description: 'Message content' })
  content: string;

  @ApiProperty({ description: 'Message status', enum: MessageStatus })
  status: MessageStatus;

  @ApiProperty({ description: 'Read at', required: false })
  readAt?: Date;

  @ApiProperty({ description: 'Delivered at', required: false })
  deliveredAt?: Date;

  @ApiProperty({ description: 'Message metadata', required: false })
  metadata?: any;

  @ApiProperty({ description: 'Message attachments', type: [MessageAttachmentDto], required: false })
  attachments?: MessageAttachmentDto[];

  @ApiProperty({ description: 'Created at' })
  createdAt: Date;

  @ApiProperty({ description: 'Updated at', required: false })
  updatedAt?: Date;

  @ApiProperty({ description: 'Actual sender info for admin messages', required: false })
  actualSender?: {
    id: string;
    name: string;
    userId: string;
    email: string;
    type: string;
  };

  @ApiProperty({ description: 'Whether this message belongs to an admin conversation', required: false })
  isAdminMessage?: boolean;

  @ApiProperty({ description: 'Whether the sender is an admin in admin conversation', required: false })
  isSenderVirtualAdmin?: boolean;
}

/**
 * DTO for creating a new message
 */
export class CreateMessageDto {
  @ApiProperty({
    description: 'Recipient ID (use "virtual-admin" for admin conversations)',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  @IsNotEmpty()
  recipientId: string;

  @ApiProperty({ description: 'Conversation ID', required: false })
  @IsUUID()
  @IsOptional()
  conversationId?: string;

  @ApiProperty({ description: 'Message type', enum: MessageType, default: MessageType.TEXT })
  @IsEnum(MessageType)
  @IsOptional()
  type?: MessageType;

  @ApiProperty({ description: 'Message content' })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({ description: 'Message metadata', required: false })
  @IsOptional()
  metadata?: any;

  @ApiProperty({ description: 'Attachment IDs', type: [String], required: false })
  @IsArray()
  @IsUUID(undefined, { each: true })
  @IsOptional()
  attachmentIds?: string[];
}

/**
 * DTO for updating a message
 */
export class UpdateMessageDto {
  @ApiProperty({ description: 'Message status', enum: MessageStatus })
  @IsEnum(MessageStatus)
  @IsOptional()
  status?: MessageStatus;

  @ApiProperty({ description: 'Message content' })
  @IsString()
  @IsOptional()
  content?: string;
}

/**
 * DTO for conversation filter
 */
export class ConversationFilterDto {
  @ApiProperty({ description: 'Search term', required: false })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({ description: 'Conversation status', enum: ConversationStatus, required: false })
  @IsEnum(ConversationStatus)
  @IsOptional()
  status?: ConversationStatus;

  @ApiProperty({ description: 'Conversation type', enum: ConversationType, required: false })
  @IsEnum(ConversationType)
  @IsOptional()
  type?: ConversationType;

  @ApiProperty({ description: 'Page number', default: 1 })
  @IsNumber()
  @IsOptional()
  page?: number;

  @ApiProperty({ description: 'Page size', default: 10 })
  @IsNumber()
  @IsOptional()
  limit?: number;
}

/**
 * DTO for message filter
 */
export class MessageFilterDto {
  @ApiProperty({ description: 'Message type', enum: MessageType, required: false })
  @IsEnum(MessageType)
  @IsOptional()
  type?: MessageType;

  @ApiProperty({ description: 'Message status', enum: MessageStatus, required: false })
  @IsEnum(MessageStatus)
  @IsOptional()
  status?: MessageStatus;

  @ApiProperty({ description: 'Search term for partial match on message content', required: false })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiProperty({ description: 'Page number', default: 1 })
  @IsNumber()
  @IsOptional()
  page?: number;

  @ApiProperty({ description: 'Page size', default: 20 })
  @IsNumber()
  @IsOptional()
  limit?: number;
}

/**
 * DTO for file upload response
 */
export class ChatFileUploadResponseDto {
  @ApiProperty({ description: 'File ID' })
  id: string;

  @ApiProperty({ description: 'File path' })
  filePath: string;

  @ApiProperty({ description: 'File name' })
  fileName: string;

  @ApiProperty({ description: 'MIME type' })
  mimeType: string;

  @ApiProperty({ description: 'File size' })
  fileSize: number;

  @ApiProperty({ description: 'File URL' })
  fileUrl: string;

  @ApiProperty({ description: 'Thumbnail URL', required: false })
  thumbnailUrl?: string;
}

/**
 * DTO for paged list of conversations
 */
export class PagedConversationListDto {
  @ApiProperty({ description: 'Conversations', type: [ConversationDto] })
  items: ConversationDto[];

  @ApiProperty({ description: 'Total count' })
  total: number;

  @ApiProperty({ description: 'Page number' })
  page: number;

  @ApiProperty({ description: 'Page size' })
  limit: number;
}

/**
 * DTO for paged list of messages
 */
export class PagedMessageListDto {
  @ApiProperty({ description: 'Messages', type: [MessageDto] })
  items: MessageDto[];

  @ApiProperty({ description: 'Total count' })
  total: number;

  @ApiProperty({ description: 'Page number' })
  page: number;

  @ApiProperty({ description: 'Page size' })
  limit: number;
}

/**
 * DTO for filtering chat contacts
 */
export class ContactFilterDto {
  @ApiProperty({ description: 'Filter by name', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Filter by email', required: false })
  @IsString()
  @IsOptional()
  email?: string;

  @ApiProperty({ description: 'Filter by phone', required: false })
  @IsString()
  @IsOptional()
  phone?: string;
}
