import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Not, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Equal, getRepository, DataSource, EntityManager } from 'typeorm';
import { Conversation, ConversationStatus, ConversationType } from '../../database/entities/conversation.entity';
import { Message, MessageStatus, MessageType } from '../../database/entities/message.entity';
import { MessageAttachment } from '../../database/entities/message-attachment.entity';
import { MessageRegistry } from '../../database/entities/message-registry.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { StudentFriendship, FriendshipStatus } from '../../database/entities/student-friendship.entity';
import { AdminConversationParticipant } from '../../database/entities/admin-conversation-participant.entity';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { AsyncNotificationHelperService } from '../notification/async-notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import {
  ConversationDto,
  MessageDto,
  CreateMessageDto,
  UpdateMessageDto,
  ConversationFilterDto,
  MessageFilterDto,
  PagedConversationListDto,
  PagedMessageListDto,
  ConversationParticipantDto,
  MessageAttachmentDto,
  ChatFileUploadResponseDto,
  ContactFilterDto,
} from '../../database/models/chat.dto';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { MulterFile } from '../../common/interfaces/multer-file.interface';
import { DEFAULT_VIRTUAL_ADMIN_USER } from './chat.constants';
import { VirtualAdminService } from './virtual-admin.service';

@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);
  private readonly uploadDir: string;

  constructor(
    @InjectRepository(Conversation)
    private readonly conversationRepository: Repository<Conversation>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(MessageAttachment)
    private readonly messageAttachmentRepository: Repository<MessageAttachment>,
    @InjectRepository(MessageRegistry)
    private readonly messageRegistryRepository: Repository<MessageRegistry>,
    @InjectRepository(AdminConversationParticipant)
    private readonly adminConversationParticipantRepository: Repository<AdminConversationParticipant>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(StudentFriendship)
    private readonly studentFriendshipRepository: Repository<StudentFriendship>,
    private readonly dataSource: DataSource,
    private readonly virtualAdminService: VirtualAdminService,
    private readonly notificationHelper: NotificationHelperService,
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
    private readonly fileRegistryService: FileRegistryService,
    private readonly deeplinkService: DeeplinkService,
    private readonly configService: ConfigService,
  ) {
    this.uploadDir = this.configService.get<string>('UPLOAD_DIR') || 'uploads';
    // Ensure the chat uploads directory exists
    const chatUploadsDir = path.join(this.uploadDir, 'chat');
    if (!fs.existsSync(chatUploadsDir)) {
      fs.mkdirSync(chatUploadsDir, { recursive: true });
    }
  }

  /**
   * Check if two users can chat with each other
   * @param userId1 First user ID
   * @param userId2 Second user ID
   * @returns True if users can chat, false otherwise
   */
  async canUsersChat(userId1: string, userId2: string): Promise<boolean> {
    try {
      // Get user types
      const [user1, user2] = await Promise.all([this.userRepository.findOne({ where: { id: userId1 } }), this.userRepository.findOne({ where: { id: userId2 } })]);

      if (!user1 || !user2) {
        return false;
      }

      // Admin can chat with anyone
      if (user1.type === UserType.ADMIN || user2.type === UserType.ADMIN) {
        return true;
      }

      // Student-Tutor: Check if they are mapped
      if ((user1.type === UserType.STUDENT && user2.type === UserType.TUTOR) || (user1.type === UserType.TUTOR && user2.type === UserType.STUDENT)) {
        const studentId = user1.type === UserType.STUDENT ? user1.id : user2.id;
        const tutorId = user1.type === UserType.TUTOR ? user1.id : user2.id;

        // Check if there's any active mapping between the student and tutor
        // regardless of the plan feature
        this.logger.log(`Checking mapping for student ${studentId} and tutor ${tutorId}`);

        // First, check for active mappings
        const activeMapping = await this.studentTutorMappingRepository.findOne({
          where: {
            studentId,
            tutorId,
            status: MappingStatus.ACTIVE,
          },
        });

        if (activeMapping) {
          this.logger.log(`Found active mapping for student ${studentId} and tutor ${tutorId} with plan feature ${activeMapping.planFeatureId}`);
          return true;
        }

        // If no active mapping, check for any mapping
        this.logger.warn(`No active mapping found for student ${studentId} and tutor ${tutorId}`);

        // Get all mappings for this student-tutor pair
        const allMappings = await this.studentTutorMappingRepository.find({
          where: {
            studentId,
            tutorId,
          },
        });

        if (allMappings.length > 0) {
          this.logger.warn(`Found ${allMappings.length} inactive mappings for student ${studentId} and tutor ${tutorId}`);
          allMappings.forEach((m) => {
            this.logger.warn(`Mapping: id=${m.id}, status=${m.status}, planFeatureId=${m.planFeatureId}`);
          });

          // Allow chat if there's any mapping, even if inactive
          return true;
        } else {
          this.logger.warn(`No mapping at all found for student ${studentId} and tutor ${tutorId}`);
          return false;
        }
      }

      // Student-Student: Check if they are friends
      if (user1.type === UserType.STUDENT && user2.type === UserType.STUDENT) {
        // Check if there's an accepted friendship between the students
        const friendship = await this.studentFriendshipRepository.findOne({
          where: [
            { requesterId: userId1, requestedId: userId2, status: FriendshipStatus.ACCEPTED },
            { requesterId: userId2, requestedId: userId1, status: FriendshipStatus.ACCEPTED },
          ],
        });

        return !!friendship;
      }

      // For now, no tutor-tutor chat
      if (user1.type === UserType.TUTOR && user2.type === UserType.TUTOR) {
        return false;
      }

      return false;
    } catch (error) {
      this.logger.error(`Error checking if users can chat: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Get or create a conversation between two users
   * @param userId1 First user ID
   * @param userId2 Second user ID
   * @returns Conversation entity
   */
  async getOrCreateConversation(userId1: string, userId2: string): Promise<Conversation> {
    try {
      // Check if users can chat
      const canChat = await this.canUsersChat(userId1, userId2);
      if (!canChat) {
        this.logger.error(`Users ${userId1} and ${userId2} cannot chat with each other`);

        // Get user types for better error message
        const [user1, user2] = await Promise.all([this.userRepository.findOne({ where: { id: userId1 } }), this.userRepository.findOne({ where: { id: userId2 } })]);

        if (user1 && user2) {
          this.logger.error(`User types: ${user1.type} (${userId1}) and ${user2.type} (${userId2})`);

          // If it's a student-tutor pair, provide more specific error
          if ((user1.type === UserType.STUDENT && user2.type === UserType.TUTOR) || (user1.type === UserType.TUTOR && user2.type === UserType.STUDENT)) {
            const studentId = user1.type === UserType.STUDENT ? user1.id : user2.id;
            const tutorId = user1.type === UserType.TUTOR ? user1.id : user2.id;

            throw new ForbiddenException(`Student ${studentId} and tutor ${tutorId} are not mapped to each other`);
          }
        }

        throw new ForbiddenException('Users cannot chat with each other');
      }

      // Check if conversation already exists
      // For admin conversations, we need to check for shared admin conversations
      const [user1, user2] = await Promise.all([
        this.userRepository.findOne({ where: { id: userId1 } }),
        this.userRepository.findOne({ where: { id: userId2 } })
      ]);

      // If one user is admin, check for existing admin conversation
      if (user1?.type === UserType.ADMIN || user2?.type === UserType.ADMIN) {
        const adminId = user1?.type === UserType.ADMIN ? userId1 : userId2;
        const targetUserId = user1?.type === UserType.ADMIN ? userId2 : userId1;

        // Use transaction to prevent race conditions
        return await this.dataSource.transaction(async (manager: EntityManager) => {
          // Check for existing admin conversation within transaction
          let conversation = await manager.findOne(Conversation, {
            where: {
              isAdminConversation: true,
              adminConversationUserId: targetUserId,
            },
          });

          if (conversation) {
            // Ensure the requesting admin is added as a participant
            await this.ensureAdminParticipantInTransaction(manager, conversation.id, adminId);
            return conversation;
          }

          // Create new admin conversation with virtual admin ID
          const virtualAdminUserId = await this.virtualAdminService.getVirtualAdminUserId();
          conversation = manager.create(Conversation, {
            participant1Id: virtualAdminUserId, // Always use virtual admin ID
            participant2Id: targetUserId,
            type: ConversationType.DIRECT,
            status: ConversationStatus.ACTIVE,
            isAdminConversation: true,
            adminConversationUserId: targetUserId,
          });

          conversation = await manager.save(conversation);

          // Add the requesting admin as a participant
          await this.ensureAdminParticipantInTransaction(manager, conversation.id, adminId);

          return conversation;
        });
      }

      // For regular conversations, check normal way
      let conversation = await this.conversationRepository.findOne({
        where: [
          { participant1Id: userId1, participant2Id: userId2, isAdminConversation: false },
          { participant1Id: userId2, participant2Id: userId1, isAdminConversation: false },
        ],
      });

      // If conversation exists, return it
      if (conversation) {
        // If conversation is archived or blocked, reactivate it
        if (conversation.status !== ConversationStatus.ACTIVE) {
          conversation.status = ConversationStatus.ACTIVE;
          conversation = await this.conversationRepository.save(conversation);
        }
        return conversation;
      }

      // Admin conversation creation is handled above in the transaction
      // This section should only handle regular user-to-user conversations

      // Create regular conversation
      const newConversation = this.conversationRepository.create({
        participant1Id: userId1,
        participant2Id: userId2,
        type: ConversationType.DIRECT,
        status: ConversationStatus.ACTIVE,
        isAdminConversation: false,
      });

      return await this.conversationRepository.save(newConversation);
    } catch (error) {
      this.logger.error(`Error getting or creating conversation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get conversations for a user
   * @param userId User ID
   * @param filter Filter options
   * @returns Paged list of conversations
   */
  async getConversations(userId: string, filter: ConversationFilterDto): Promise<PagedConversationListDto> {
    try {
      const page = filter.page || 1;
      const limit = filter.limit || 10;
      const skip = (page - 1) * limit;

      // Build query
      const queryBuilder = this.conversationRepository
        .createQueryBuilder('conversation')
        .leftJoinAndSelect('conversation.participant1', 'participant1')
        .leftJoinAndSelect('conversation.participant2', 'participant2')
        .where('(conversation.participant1Id = :userId OR conversation.participant2Id = :userId)', { userId })
        .orderBy('conversation.lastMessageAt', 'DESC')
        .addOrderBy('conversation.createdAt', 'DESC');

      // Apply filters
      if (filter.status) {
        queryBuilder.andWhere('conversation.status = :status', { status: filter.status });
      }

      if (filter.type) {
        queryBuilder.andWhere('conversation.type = :type', { type: filter.type });
      }

      if (filter.search) {
        queryBuilder.andWhere('(participant1.name ILIKE :search OR participant2.name ILIKE :search OR participant1.userId ILIKE :search OR participant2.userId ILIKE :search)', {
          search: `%${filter.search}%`,
        });
      }

      // Get total count
      const total = await queryBuilder.getCount();

      // Get conversations
      const conversations = await queryBuilder.skip(skip).take(limit).getMany();

      // Map to DTOs
      const conversationDtos = await Promise.all(
        conversations.map(async (conversation) => {
          return this.mapConversationToDto(conversation, userId);
        }),
      );

      return {
        items: conversationDtos,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Error getting conversations: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a conversation by ID
   * @param conversationId Conversation ID
   * @param userId User ID (for authorization)
   * @returns Conversation DTO
   */
  async getConversation(conversationId: string, userId: string): Promise<ConversationDto> {
    try {
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId },
        relations: ['participant1', 'participant2'],
      });

      if (!conversation) {
        throw new NotFoundException('Conversation not found');
      }

      // Check if user is a participant (optimized)
      const isParticipant = await this.validateUserParticipation(conversation, userId);
      if (!isParticipant) {
        throw new ForbiddenException('User is not a participant in this conversation');
      }

      // If there's no last message in the conversation entity, try to fetch the latest message
      if (!conversation.lastMessageText && conversation.id) {
        try {
          const latestMessage = await this.messageRepository.findOne({
            where: { conversationId: conversation.id },
            order: { createdAt: 'DESC' },
          });

          if (latestMessage) {
            conversation.lastMessageText = latestMessage.content;
            conversation.lastMessageAt = latestMessage.createdAt;
            conversation.lastMessageSenderId = latestMessage.senderId;
          }
        } catch (error) {
          this.logger.error(`Error fetching latest message for conversation ${conversation.id}: ${error.message}`, error.stack);
        }
      }

      return this.mapConversationToDto(conversation, userId);
    } catch (error) {
      this.logger.error(`Error getting conversation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get messages for a conversation
   * @param conversationId Conversation ID
   * @param userId User ID (for authorization)
   * @param filter Filter options
   * @returns Paged list of messages
   */
  async getMessages(conversationId: string, userId: string, filter: MessageFilterDto): Promise<PagedMessageListDto> {
    try {
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId },
      });

      if (!conversation) {
        // Return empty result set instead of throwing an error
        this.logger.warn(`Conversation ${conversationId} not found, returning empty result set`);
        return {
          items: [],
          total: 0,
          page: filter.page || 1,
          limit: filter.limit || 20,
        };
      }

      // Check if user is a participant (optimized)
      const isParticipant = await this.validateUserParticipation(conversation, userId);
      if (!isParticipant) {
        throw new ForbiddenException('User is not a participant in this conversation');
      }

      const page = filter.page || 1;
      const limit = filter.limit || 20;
      const skip = (page - 1) * limit;

      // Build query
      const queryBuilder = this.messageRepository
        .createQueryBuilder('message')
        .leftJoinAndSelect('message.sender', 'sender')
        .leftJoinAndSelect('message.actualSender', 'actualSender')
        .leftJoinAndSelect('message.attachments', 'attachments')
        .where('message.conversationId = :conversationId', { conversationId })
        .orderBy('message.createdAt', 'DESC');

      // Apply filters
      if (filter.type) {
        queryBuilder.andWhere('message.type = :type', { type: filter.type });
      }

      if (filter.status) {
        queryBuilder.andWhere('message.status = :status', { status: filter.status });
      }

      if (filter.search) {
        queryBuilder.andWhere('message.content ILIKE :search', { search: `%${filter.search}%` });
      }

      // Get total count
      const total = await queryBuilder.getCount();

      // Get messages
      const messages = await queryBuilder.skip(skip).take(limit).getMany();

      // Mark messages as read
      await this.markMessagesAsRead(conversationId, userId);

      // Use optimized batch mapping for better performance
      const messageDtos = await this.mapMessagesToDto(messages);

      return {
        items: messageDtos,
        total,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(`Error getting messages: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send a message
   * @param senderId Sender ID
   * @param createMessageDto Message data
   * @returns Created message
   */
  async sendMessage(senderId: string, createMessageDto: CreateMessageDto): Promise<MessageDto> {
    try {
      const { recipientId, type = MessageType.TEXT, content, metadata, attachmentIds } = createMessageDto;

      // Handle virtual admin messages specially
      const virtualAdminUserId = await this.virtualAdminService.getVirtualAdminUserId();
      if (recipientId === virtualAdminUserId) {
        return await this.sendMessageToVirtualAdmin(senderId, createMessageDto);
      }

      // Check if users can chat
      const canChat = await this.canUsersChat(senderId, recipientId);
      if (!canChat) {
        this.logger.error(`Users ${senderId} and ${recipientId} cannot chat with each other`);

        // Get user types for better error message
        const [sender, recipient] = await Promise.all([this.userRepository.findOne({ where: { id: senderId } }), this.userRepository.findOne({ where: { id: recipientId } })]);

        if (sender && recipient) {
          this.logger.error(`User types: ${sender.type} (${senderId}) and ${recipient.type} (${recipientId})`);

          // If it's a student-tutor pair, provide more specific error
          if ((sender.type === UserType.STUDENT && recipient.type === UserType.TUTOR) || (sender.type === UserType.TUTOR && recipient.type === UserType.STUDENT)) {
            const studentId = sender.type === UserType.STUDENT ? sender.id : recipient.id;
            const tutorId = sender.type === UserType.TUTOR ? sender.id : recipient.id;

            throw new ForbiddenException(`Student ${studentId} and tutor ${tutorId} are not mapped to each other`);
          }
        }

        throw new ForbiddenException('Users cannot chat with each other');
      }

      // Get or create conversation
      const conversation = await this.getOrCreateConversation(senderId, recipientId);

      // Create message
      const message = this.messageRepository.create({
        conversationId: conversation.id,
        senderId,
        recipientId,
        type,
        content,
        metadata,
        status: MessageStatus.SENT,
      });

      const savedMessage = await this.messageRepository.save(message);

      // Process attachments if any
      if (attachmentIds && attachmentIds.length > 0) {
        await this.processMessageAttachments(savedMessage.id, attachmentIds);
      }

      // Update conversation with last message info
      conversation.lastMessageAt = savedMessage.createdAt;
      conversation.lastMessageText = content;
      conversation.lastMessageSenderId = senderId;

      // Update unread counts
      if (conversation.participant1Id === recipientId) {
        conversation.participant1UnreadCount += 1;
      } else {
        conversation.participant2UnreadCount += 1;
      }

      await this.conversationRepository.save(conversation);

      // Get message with attachments
      const messageWithAttachments = await this.messageRepository.findOne({
        where: { id: savedMessage.id },
        relations: ['sender', 'actualSender', 'attachments'],
      });

      // Send notification to recipient
      await this.sendMessageNotification(messageWithAttachments);

      // Regular conversations are not admin conversations
      // For single message, create a small profile picture map for consistency
      const profilePictureMap = new Map<string, string>();
      if (messageWithAttachments.sender?.id) {
        try {
          const profilePictureUrl = this.deeplinkService.getProfilePictureUrl(messageWithAttachments.sender.id);
          profilePictureMap.set(messageWithAttachments.sender.id, profilePictureUrl);
        } catch (error) {
          this.logger.warn(`Failed to get profile picture URL for sender ${messageWithAttachments.sender.id}: ${error.message}`);
        }
      }

      return await this.mapMessageToDto(messageWithAttachments, false, undefined, profilePictureMap);
    } catch (error) {
      this.logger.error(`Error sending message: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send message to virtual admin (admin conversation)
   * @param senderId User sending the message
   * @param createMessageDto Message data
   * @returns Created message
   */
  private async sendMessageToVirtualAdmin(senderId: string, createMessageDto: CreateMessageDto): Promise<MessageDto> {
    try {
      // Find the admin conversation for this user
      const adminConversation = await this.conversationRepository.findOne({
        where: {
          isAdminConversation: true,
          adminConversationUserId: senderId,
        },
        relations: ['participant1', 'participant2'],
      });

      if (!adminConversation) {
        throw new NotFoundException('No admin conversation found. Please wait for an admin to contact you first.');
      }

      // Create the message in the admin conversation
      const message = this.messageRepository.create({
        conversationId: adminConversation.id,
        senderId: senderId,
        recipientId: adminConversation.participant1Id, // Send to the admin side
        type: createMessageDto.type || MessageType.TEXT,
        content: createMessageDto.content,
        metadata: createMessageDto.metadata,
        status: MessageStatus.SENT,
      });

      const savedMessage = await this.messageRepository.save(message);

      // Process attachments if any
      if (createMessageDto.attachmentIds && createMessageDto.attachmentIds.length > 0) {
        await this.processMessageAttachments(savedMessage.id, createMessageDto.attachmentIds);
      }

      // Update conversation last message info
      await this.conversationRepository.update(adminConversation.id, {
        lastMessageAt: savedMessage.createdAt,
        lastMessageText: savedMessage.content,
        lastMessageSenderId: savedMessage.senderId,
        // Don't increment unread count for participant2 since the user is sending the message
      });

      // Get the message with relations for response
      const messageWithAttachments = await this.messageRepository.findOne({
        where: { id: savedMessage.id },
        relations: ['sender', 'actualSender', 'attachments'],
      });

      // Send notification to all active admin participants
      await this.sendAdminNotification(messageWithAttachments, adminConversation.id);

      // Virtual admin messages are always admin messages
      // For single message, create a small profile picture map for consistency
      const profilePictureMap = new Map<string, string>();
      if (messageWithAttachments.sender?.id) {
        try {
          const profilePictureUrl = this.deeplinkService.getProfilePictureUrl(messageWithAttachments.sender.id);
          profilePictureMap.set(messageWithAttachments.sender.id, profilePictureUrl);
        } catch (error) {
          this.logger.warn(`Failed to get profile picture URL for sender ${messageWithAttachments.sender.id}: ${error.message}`);
        }
      }
      if (messageWithAttachments.actualSender?.id) {
        try {
          const profilePictureUrl = this.deeplinkService.getProfilePictureUrl(messageWithAttachments.actualSender.id);
          profilePictureMap.set(messageWithAttachments.actualSender.id, profilePictureUrl);
        } catch (error) {
          this.logger.warn(`Failed to get profile picture URL for actual sender ${messageWithAttachments.actualSender.id}: ${error.message}`);
        }
      }

      return await this.mapMessageToDto(messageWithAttachments, true, undefined, profilePictureMap);
    } catch (error) {
      this.logger.error(`Error sending message to virtual admin: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process message attachments
   * @param messageId Message ID
   * @param attachmentIds Attachment IDs (from message registry)
   */
  private async processMessageAttachments(messageId: string, attachmentIds: string[]): Promise<void> {
    try {
      // Get registry entries
      const registryEntries = await this.messageRegistryRepository.find({
        where: { id: In(attachmentIds), isTemporary: true },
      });

      if (registryEntries.length === 0) {
        return;
      }

      // Create message attachments
      const attachments = registryEntries.map((registry) => {
        return this.messageAttachmentRepository.create({
          messageId,
          filePath: registry.filePath,
          fileName: registry.fileName,
          mimeType: registry.mimeType,
          fileSize: registry.fileSize,
          thumbnailPath: registry.thumbnailPath,
        });
      });

      await this.messageAttachmentRepository.save(attachments);

      // Update registry entries to mark them as permanent
      await Promise.all(
        registryEntries.map(async (registry) => {
          registry.isTemporary = false;
          registry.messageId = messageId;
          return this.messageRegistryRepository.save(registry);
        }),
      );
    } catch (error) {
      this.logger.error(`Error processing message attachments: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send notification to admin participants for messages from users
   * @param message Message entity
   * @param conversationId Admin conversation ID
   */
  private async sendAdminNotification(message: Message, conversationId: string): Promise<void> {
    try {
      // Get all active admin participants for this conversation
      const adminParticipants = await this.adminConversationParticipantRepository.find({
        where: { conversationId, isActive: true },
        relations: ['admin'],
      });

      if (adminParticipants.length === 0) {
        this.logger.warn(`No active admin participants found for conversation ${conversationId}`);
        return;
      }

      const sender = message.sender || (await this.userRepository.findOne({ where: { id: message.senderId } }));
      if (!sender) {
        this.logger.warn(`Sender not found for message ${message.id}`);
        return;
      }

      const notificationTitle = `New message from ${sender.name}`;
      let notificationContent = message.content;

      // Customize notification based on message type
      if (message.type === MessageType.IMAGE) {
        notificationContent = 'Sent you an image';
      } else if (message.type === MessageType.FILE) {
        notificationContent = 'Sent you a file';
      } else if (message.type === MessageType.QUIZ) {
        notificationContent = 'Sent you a quiz';
      }

      // Truncate content if too long
      if (notificationContent.length > 100) {
        notificationContent = notificationContent.substring(0, 97) + '...';
      }

      // Send notification to all admin participants
      for (const participant of adminParticipants) {
        if (participant.admin) {
          await this.asyncNotificationHelper.notifyAsync(
            participant.admin.id,
            NotificationType.CHAT_MESSAGE,
            notificationTitle,
            notificationContent,
            {
              relatedEntityId: message.id,
              relatedEntityType: 'chat_message',
              sendInApp: true,
              sendPush: true,
              sendRealtime: true,
            },
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error sending admin notification: ${error.message}`, error.stack);
    }
  }

  /**
   * Send notification for a new message
   * @param message Message entity
   */
  private async sendMessageNotification(message: Message): Promise<void> {
    try {
      const sender = message.sender || (await this.userRepository.findOne({ where: { id: message.senderId } }));
      if (!sender) {
        return;
      }

      const notificationTitle = `New message from ${sender.name}`;
      let notificationContent = message.content;

      // Customize notification based on message type
      if (message.type === MessageType.IMAGE) {
        notificationContent = 'Sent you an image';
      } else if (message.type === MessageType.FILE) {
        notificationContent = 'Sent you a file';
      } else if (message.type === MessageType.QUIZ) {
        notificationContent = 'Sent you a quiz';
      }

      // Truncate content if too long
      if (notificationContent.length > 100) {
        notificationContent = notificationContent.substring(0, 97) + '...';
      }

      await this.notificationHelper.notify(message.recipientId, NotificationType.CHAT_MESSAGE, notificationTitle, notificationContent, {
        relatedEntityId: message.id,
        relatedEntityType: 'message',
        sendEmail: false,
        sendPush: true,
        sendInApp: true,
        sendMobile: true,
        sendSms: false,
        sendRealtime: true,
      });
    } catch (error) {
      this.logger.error(`Error sending message notification: ${error.message}`, error.stack);
    }
  }

  /**
   * Mark messages as read
   * @param conversationId Conversation ID
   * @param userId User ID (recipient)
   */
  async markMessagesAsRead(conversationId: string, userId: string): Promise<void> {
    try {
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId },
      });

      if (!conversation) {
        throw new NotFoundException('Conversation not found');
      }

      // Check if user is a participant (optimized)
      const isParticipant = await this.validateUserParticipation(conversation, userId);
      if (!isParticipant) {
        throw new ForbiddenException('User is not a participant in this conversation');
      }

      // Update unread messages
      await this.messageRepository.update(
        {
          conversationId,
          recipientId: userId,
          status: MessageStatus.SENT,
        },
        {
          status: MessageStatus.READ,
          readAt: getCurrentUTCDate(),
        },
      );

      // Reset unread count for the user
      if (conversation.participant1Id === userId) {
        conversation.participant1UnreadCount = 0;
      } else {
        conversation.participant2UnreadCount = 0;
      }

      await this.conversationRepository.save(conversation);
    } catch (error) {
      this.logger.error(`Error marking messages as read: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Mark messages as delivered
   * @param conversationId Conversation ID
   * @param userId User ID (recipient)
   */
  async markMessagesAsDelivered(conversationId: string, userId: string): Promise<void> {
    try {
      const conversation = await this.conversationRepository.findOne({
        where: { id: conversationId },
      });

      if (!conversation) {
        throw new NotFoundException('Conversation not found');
      }

      // Check if user is a participant (optimized)
      const isParticipant = await this.validateUserParticipation(conversation, userId);
      if (!isParticipant) {
        throw new ForbiddenException('User is not a participant in this conversation');
      }

      // Update sent messages to delivered
      await this.messageRepository.update(
        {
          conversationId,
          recipientId: userId,
          status: MessageStatus.SENT,
        },
        {
          status: MessageStatus.DELIVERED,
          deliveredAt: getCurrentUTCDate(),
        },
      );
    } catch (error) {
      this.logger.error(`Error marking messages as delivered: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Upload a file for a message using FileRegistryService for consistency
   * @param userId User ID
   * @param file File to upload
   * @returns File upload response
   */
  async uploadFile(userId: string, file: MulterFile): Promise<ChatFileUploadResponseDto> {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Use FileRegistryService for consistent upload handling
      const result = await this.fileRegistryService.uploadFile(FileEntityType.MESSAGE_ATTACHMENT, file, userId, { userId, isTemporary: true });

      // Generate consistent URLs using FileRegistryService
      const fileUrl = await this.fileRegistryService.getMessageAttachmentUrl(result.registry.id);
      const mediaControllerUrl = await this.fileRegistryService.getFileUrl(FileEntityType.MESSAGE_ATTACHMENT, result.registry.id);

      return {
        id: result.registry.id,
        filePath: mediaControllerUrl, // Use media controller URL instead of file system path
        fileName: result.registry.fileName,
        mimeType: result.registry.mimeType,
        fileSize: result.registry.fileSize,
        fileUrl,
      };
    } catch (error) {
      this.logger.error(`Error uploading file: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get file by ID (deprecated - use media controller instead)
   * @param fileId File ID (registry ID)
   * @returns File buffer and metadata
   */
  async getFile(fileId: string): Promise<{ buffer: Buffer; fileName: string; mimeType: string }> {
    try {
      // Use FileRegistryService for consistent file handling
      return await this.fileRegistryService.getFileBuffer(FileEntityType.MESSAGE_ATTACHMENT, fileId);
    } catch (error) {
      this.logger.error(`Error getting file: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get available chat contacts for a user
   * @param userId User ID
   * @param filter Filter options
   * @returns List of users who can chat with the given user
   */
  async getChatContacts(userId: string, filter?: ContactFilterDto): Promise<ConversationParticipantDto[]> {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Use a Map to prevent duplicates by user ID
      const contactsMap = new Map<string, User>();

      // If user is admin, get all users
      if (user.type === UserType.ADMIN) {
        const allUsers = await this.userRepository.find({
          where: { id: Not(Equal(userId)) },
        });
        allUsers.forEach(contact => contactsMap.set(contact.id, contact));
      }
      // If user is student, get assigned tutors and friends
      else if (user.type === UserType.STUDENT) {
        this.logger.log(`Getting tutors for student ${userId}`);
        const mappings = await this.studentTutorMappingRepository.find({
          where: { studentId: userId, status: MappingStatus.ACTIVE },
          relations: ['tutor'],
        });
        this.logger.log(`Found ${mappings.length} active tutor mappings for student ${userId}`);

        // Add tutors to contacts map
        mappings.forEach((mapping) => {
          if (mapping.tutor) {
            contactsMap.set(mapping.tutor.id, mapping.tutor);
            this.logger.log(`Added tutor: ${mapping.tutor.id} (${mapping.tutor.name})`);
          }
        });

        // Also get student friends
        const friendships = await this.studentFriendshipRepository.find({
          where: [
            { requesterId: userId, status: FriendshipStatus.ACCEPTED },
            { requestedId: userId, status: FriendshipStatus.ACCEPTED },
          ],
          relations: ['requester', 'requested'],
        });

        // Add friends to contacts map (prevents duplicates automatically)
        friendships.forEach((friendship) => {
          const friendId = friendship.requesterId === userId ? friendship.requestedId : friendship.requesterId;
          const friend = friendship.requesterId === userId ? friendship.requested : friendship.requester;

          if (friend && !contactsMap.has(friendId)) {
            contactsMap.set(friendId, friend);
            this.logger.log(`Added friend: ${friendId} (${friend.name})`);
          }
        });
      }
      // If user is tutor, get assigned students
      else if (user.type === UserType.TUTOR) {
        this.logger.log(`Getting students for tutor ${userId}`);
        const mappings = await this.studentTutorMappingRepository.find({
          where: { tutorId: userId, status: MappingStatus.ACTIVE },
          relations: ['student'],
        });
        this.logger.log(`Found ${mappings.length} active student mappings for tutor ${userId}`);

        // Add students to contacts map
        mappings.forEach((mapping) => {
          if (mapping.student) {
            contactsMap.set(mapping.student.id, mapping.student);
            this.logger.log(`Added student: ${mapping.student.id} (${mapping.student.name})`);
          }
        });
      }

      // For non-admin users, check if they have admin conversations and add virtual admin contact
      if (user.type !== UserType.ADMIN) {
        const adminConversation = await this.conversationRepository.findOne({
          where: {
            isAdminConversation: true,
            adminConversationUserId: userId,
          },
          select: ['id', 'isAdminConversation'], // Only select needed fields for performance
        });

        if (adminConversation) {
          // Get the virtual admin details
          const virtualAdminEntity = await this.virtualAdminService.getVirtualAdmin();
          const virtualAdmin = {
            ...virtualAdminEntity.user,
            name: virtualAdminEntity.displayName,
            type: UserType.ADMIN,
          } as User;

          // Add virtual admin to contacts map (prevents duplicates automatically)
          if (!contactsMap.has(virtualAdminEntity.userId)) {
            contactsMap.set(virtualAdminEntity.userId, virtualAdmin);
          }
        }
      }

      // Convert map to array
      let contacts = Array.from(contactsMap.values());

      // Apply filters if provided
      if (filter) {
        if (filter.name) {
          contacts = contacts.filter((contact) => contact.name.toLowerCase().includes(filter.name.toLowerCase()));
        }
        if (filter.email) {
          contacts = contacts.filter((contact) => contact.email.toLowerCase().includes(filter.email.toLowerCase()));
        }
        if (filter.phone) {
          contacts = contacts.filter((contact) => contact.phoneNumber && contact.phoneNumber.includes(filter.phone));
        }
      }

      // Find or create conversations for each contact
      const contactDtos = await Promise.all(
        contacts.map(async (contact) => {
          try {
            let conversation = null;

            // Handle virtual admin contact specially
            const virtualAdminUserId = await this.virtualAdminService.getVirtualAdminUserId();
            if (contact.id === virtualAdminUserId) {
              // Find the admin conversation for this user
              conversation = await this.conversationRepository.findOne({
                where: {
                  isAdminConversation: true,
                  adminConversationUserId: userId,
                },
              });

              if (!conversation) {
                this.logger.warn(`No admin conversation found for user ${userId}`);
                return null;
              }
            } else {
              // Find existing conversation for regular contacts
              conversation = await this.conversationRepository.findOne({
                where: [
                  { participant1Id: userId, participant2Id: contact.id, isAdminConversation: false },
                  { participant1Id: contact.id, participant2Id: userId, isAdminConversation: false },
                ],
              });

              // If no conversation exists, try to create one (but don't force creation)
              if (!conversation) {
                // Check if users can chat first
                const canChat = await this.canUsersChat(userId, contact.id);
                if (canChat) {
                  try {
                    // Use a transaction to prevent race conditions
                    conversation = await this.dataSource.transaction(async (manager: EntityManager) => {
                      // Double-check if conversation was created by another request
                      const existingConversation = await manager.findOne(Conversation, {
                        where: [
                          { participant1Id: userId, participant2Id: contact.id, isAdminConversation: false },
                          { participant1Id: contact.id, participant2Id: userId, isAdminConversation: false },
                        ],
                      });

                      if (existingConversation) {
                        return existingConversation;
                      }

                      // Create new conversation
                      const newConversation = manager.create(Conversation, {
                        participant1Id: userId,
                        participant2Id: contact.id,
                        type: ConversationType.DIRECT,
                        status: ConversationStatus.ACTIVE,
                        isAdminConversation: false,
                      });

                      return await manager.save(newConversation);
                    });

                    this.logger.log(`Created new conversation ${conversation.id} between users ${userId} and ${contact.id}`);
                  } catch (error) {
                    this.logger.error(`Error creating conversation between ${userId} and ${contact.id}: ${error.message}`);
                    // If conversation creation fails, still return the contact without conversationId
                    conversation = null;
                  }
                } else {
                  this.logger.warn(`Users ${userId} and ${contact.id} cannot chat with each other`);
                  return null;
                }
              }
            }

            // Get last message and time if conversation exists
            let lastMessage = null;
            let lastMessageTime = null;

            if (conversation) {
              lastMessage = conversation.lastMessageText;
              lastMessageTime = conversation.lastMessageAt;

              // If there's no last message in the conversation entity, try to fetch the latest message
              if (!lastMessage && conversation.id) {
                try {
                  const latestMessage = await this.messageRepository.findOne({
                    where: { conversationId: conversation.id },
                    order: { createdAt: 'DESC' },
                  });

                  if (latestMessage) {
                    lastMessage = latestMessage.content;
                    lastMessageTime = latestMessage.createdAt;
                  }
                } catch (error) {
                  this.logger.error(`Error fetching latest message for conversation ${conversation.id}: ${error.message}`, error.stack);
                }
              }
            }

            // For virtual admin, adjust the unread count to show from user's perspective
            let unreadCount = 0;
            const virtualAdminUserIdForUnread = await this.virtualAdminService.getVirtualAdminUserId();
            if (contact.id === virtualAdminUserIdForUnread && conversation) {
              // For admin conversations, the unread count is stored in participant2UnreadCount
              unreadCount = conversation.participant2UnreadCount || 0;
            }

            return {
              id: contact.id,
              name: contact.name,
              userId: contact.userId,
              email: contact.email,
              type: contact.type,
              profilePicture: contact.profilePicture,
              conversationId: conversation?.id, // Include conversation ID
              lastMessage: lastMessage, // Include last message
              lastMessageTime: lastMessageTime, // Include last message time
              unreadCount: unreadCount, // Include unread count for virtual admin
              isOnline: false, // Default to offline
            };
          } catch (error) {
            this.logger.error(`Error processing contact ${contact.id}: ${error.message}`, error.stack);
            return null;
          }
        }),
      );

      // Filter out null values (contacts that couldn't get a conversation)
      return contactDtos.filter((dto) => dto !== null);
    } catch (error) {
      this.logger.error(`Error getting chat contacts: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Map conversation entity to DTO
   * @param conversation Conversation entity
   * @param currentUserId Current user ID
   * @returns Conversation DTO
   */
  private async mapConversationToDto(conversation: Conversation, currentUserId: string): Promise<ConversationDto> {
    // Determine the other participant
    const otherParticipantId = conversation.participant1Id === currentUserId ? conversation.participant2Id : conversation.participant1Id;

    // Get other participant
    const otherParticipant = conversation.participant1Id === currentUserId ? conversation.participant2 : conversation.participant1;

    // If other participant is not loaded, fetch it
    const participant = otherParticipant || (await this.userRepository.findOne({ where: { id: otherParticipantId } }));

    if (!participant) {
      throw new NotFoundException('Participant not found');
    }

    // Determine unread count for current user
    const unreadCount = conversation.participant1Id === currentUserId ? conversation.participant1UnreadCount : conversation.participant2UnreadCount;

    return {
      id: conversation.id,
      type: conversation.type,
      status: conversation.status,
      participant: {
        id: participant.id,
        name: participant.name,
        userId: participant.userId,
        email: participant.email,
        type: participant.type,
        profilePicture: participant.profilePicture,
        unreadCount,
      },
      lastMessageAt: conversation.lastMessageAt,
      lastMessageText: conversation.lastMessageText,
      lastMessageSenderId: conversation.lastMessageSenderId,
      unreadCount,
      createdAt: conversation.createdAt,
    };
  }

  /**
   * Efficiently map multiple messages to DTOs with batch conversation lookup
   * @param messages Array of message entities
   * @returns Array of message DTOs
   */
  private async mapMessagesToDto(messages: Message[]): Promise<MessageDto[]> {
    if (messages.length === 0) return [];

    // Get unique conversation IDs
    const conversationIds = [...new Set(messages.map(m => m.conversationId))];

    // Batch fetch conversation admin status
    const conversations = await this.conversationRepository.find({
      where: { id: In(conversationIds) },
      select: ['id', 'isAdminConversation'],
    });

    // Create lookup map for O(1) access
    const conversationMap = new Map(
      conversations.map(c => [c.id, c.isAdminConversation || false])
    );

    // Check if any messages are admin messages
    const hasAdminMessages = conversations.some(conv => conv.isAdminConversation);

    // Pre-fetch virtual admin details if needed (single query for all admin messages)
    let virtualAdminDetails: { userId: string;isSenderVirtualAdmin:boolean; displayName: string; profilePicture: string | null } | undefined;
    if (hasAdminMessages) {
      try {
        virtualAdminDetails = await this.virtualAdminService.getVirtualAdminForMessage();
      } catch (error) {
        this.logger.warn(`Failed to pre-fetch virtual admin details: ${error.message}`);
      }
    }

    // Batch fetch profile picture URLs for all unique user IDs
    const uniqueUserIds = new Set<string>();
    messages.forEach(message => {
      //if (message.sender?.id) uniqueUserIds.add(message.sender.id);
      if (message.actualSender?.id) uniqueUserIds.add(message.actualSender.id);
    });

    // Batch fetch profile picture URLs using DeeplinkService for performance
    const profilePictureMap = new Map<string, string>();
    if (uniqueUserIds.size > 0) {
      try {
        await Promise.all(
          Array.from(uniqueUserIds).map(async (userId) => {
            try {
              const profilePictureUrl = this.deeplinkService.getProfilePictureUrl(userId);
              
              profilePictureMap.set(userId, profilePictureUrl);
            } catch (error) {
              this.logger.warn(`Failed to get profile picture URL for user ${userId}: ${error.message}`);
              profilePictureMap.set(userId, null);
            }
          })
        );
      } catch (error) {
        this.logger.error(`Error batch fetching profile pictures: ${error.message}`);
      }
    }

    // Map all messages with pre-fetched conversation info, virtual admin details, and profile pictures
    return Promise.all(
      messages.map(message => {
        const isAdminMessage = conversationMap.get(message.conversationId) || false;
        return this.mapMessageToDto(message, isAdminMessage, virtualAdminDetails, profilePictureMap);
      })
    );
  }

  /**
   * Map message entity to DTO with consistent file URLs (optimized)
   * @param message Message entity
   * @param isAdminMessage Whether this message belongs to an admin conversation (optional, will query if not provided)
   * @param virtualAdminDetails Pre-fetched virtual admin details for performance
   * @param profilePictureMap Pre-fetched profile picture URLs map for performance
   * @returns Message DTO
   */
  private async mapMessageToDto(
    message: Message,
    isAdminMessage?: boolean,
    virtualAdminDetails?: { userId: string;isSenderVirtualAdmin:boolean; displayName: string; profilePicture: string | null },
    profilePictureMap?: Map<string, string>
  ): Promise<MessageDto> {
    const sender = message.sender;

    // Only query for admin conversation status if not provided
    let adminMessageFlag = isAdminMessage;
    if (adminMessageFlag === undefined) {
      const conversation = await this.conversationRepository.findOne({
        where: { id: message.conversationId },
        select: ['isAdminConversation'],
      });
      adminMessageFlag = conversation?.isAdminConversation || false;
    }

    // Map attachments to DTOs first
    const attachmentDtos =
      message.attachments?.map((attachment) => ({
        id: attachment.id,
        filePath: attachment.filePath, // Will be replaced with media controller URL
        fileName: attachment.fileName,
        mimeType: attachment.mimeType,
        fileSize: attachment.fileSize,
        thumbnailPath: attachment.thumbnailPath,
        fileUrl: '', // Will be set below
        thumbnailUrl: undefined, // Will be set below
      })) || [];

    // Generate file URLs for all attachments (same pattern as shop items)
    await Promise.all(
      attachmentDtos.map(async (dto, index) => {
        const attachment = message.attachments[index];
        try {
          // Find the corresponding MessageRegistry entry for this attachment
          const registryEntry = await this.messageRegistryRepository.findOne({
            where: {
              messageId: message.id,
              fileName: attachment.fileName,
              isTemporary: false,
            },
          });

          if (registryEntry) {
            // Use FileRegistryService to get the URL (same as shop items)
            const fileUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.MESSAGE_ATTACHMENT, registryEntry.id);

            if (fileUrl) {
              dto.id = registryEntry.id; // Use registry ID
              dto.filePath = fileUrl; // Set media controller URL
              dto.fileUrl = fileUrl; // Set same URL for fileUrl
              dto.thumbnailUrl = attachment.thumbnailPath ? fileUrl : undefined;
            }
          } else {
            this.logger.error(`Registry entry not found for attachment ${attachment.id} in message ${message.id}. This indicates a data integrity issue.`);
          }
        } catch (error) {
          this.logger.error(`Error generating file URL for attachment ${attachment.id}: ${error.message}`, error.stack);
        }
      }),
    );

    // For admin messages, use virtual admin details for unified identity
    let senderId = message.senderId;
    let senderName = sender?.name || 'Unknown';
    let senderProfilePicture = sender?.profilePicture;
    let isSenderVirtualAdmin = false;

    // Use pre-fetched profile picture URLs for performance
    if (profilePictureMap) {
      if (message.actualSender?.id) {
        // For admin messages, prioritize actual sender's profile picture
        senderProfilePicture = profilePictureMap.get(message.actualSender.id) || null;
      } else if (sender?.id) {
        // For regular messages, use sender's profile picture
        senderProfilePicture = profilePictureMap.get(sender.id) || null;
      }
    } else {
      // Fallback to entity profile picture fields (less optimal)
      if (message.actualSender) {
        senderProfilePicture = message.actualSender.profilePicture;
      }
    }

    if (adminMessageFlag && message.actualSenderId) {
      // This is an admin message, use virtual admin details
      if (virtualAdminDetails) {
        // Use pre-fetched details for performance
        senderId = virtualAdminDetails.userId;
        isSenderVirtualAdmin = virtualAdminDetails.isSenderVirtualAdmin;
        senderName = virtualAdminDetails.displayName;
        senderProfilePicture = virtualAdminDetails.profilePicture;
      } else {
        // Fallback to fetching (less efficient)
        try {
          const virtualAdminData = await this.virtualAdminService.getVirtualAdminForMessage();
          senderId = virtualAdminData.userId;
          isSenderVirtualAdmin = virtualAdminData.isSenderVirtualAdmin;
          senderName = virtualAdminData.displayName;
          senderProfilePicture = virtualAdminData.profilePicture;
        } catch (error) {
          this.logger.warn(`Failed to get virtual admin details for message ${message.id}, using fallback: ${error.message}`);
          // Fallback to default admin details
          senderId ='virtual-admin';
          senderName = 'HEC Admin';
          isSenderVirtualAdmin = true;
          senderProfilePicture = null;
        }
      }
    }

    return {
      id: message.id,
      conversationId: message.conversationId,
      senderId: senderId,
      senderName: senderName,
      senderProfilePicture: senderProfilePicture,
      recipientId: message.recipientId,
      type: message.type,
      content: message.content,
      status: message.status,
      readAt: message.readAt,
      deliveredAt: message.deliveredAt,
      metadata: message.metadata,
      attachments: attachmentDtos,
      createdAt: message.createdAt,
      isAdminMessage: adminMessageFlag,
      isSenderVirtualAdmin: isSenderVirtualAdmin, // Admin chat service always handles admin messages
      actualSender: message.actualSender ? {
        id: message.actualSender.id,
        name: message.actualSender.name,
        userId: message.actualSender.userId,
        email: message.actualSender.email,
        type: message.actualSender.type,
      } : undefined,
    };
  }

  /**
   * Ensure an admin is added as a participant in an admin conversation
   * @param conversationId The conversation ID
   * @param adminId The admin ID
   */
  private async ensureAdminParticipant(conversationId: string, adminId: string): Promise<void> {
    const existingParticipant = await this.adminConversationParticipantRepository.findOne({
      where: { conversationId, adminId },
    });

    if (!existingParticipant) {
      const adminParticipant = this.adminConversationParticipantRepository.create({
        conversationId,
        adminId,
        isActive: true,
        lastAccessedAt: new Date(),
      });

      await this.adminConversationParticipantRepository.save(adminParticipant);
      this.logger.log(`Added admin ${adminId} as participant in conversation ${conversationId}`);
    } else if (!existingParticipant.isActive) {
      // Reactivate if inactive
      await this.adminConversationParticipantRepository.update(
        { conversationId, adminId },
        { isActive: true, lastAccessedAt: new Date() },
      );
      this.logger.log(`Reactivated admin ${adminId} in conversation ${conversationId}`);
    }
  }

  /**
   * Ensure an admin is added as a participant to an admin conversation within a transaction
   * @param manager The transaction manager
   * @param conversationId The conversation ID
   * @param adminId The admin ID
   */
  private async ensureAdminParticipantInTransaction(manager: EntityManager, conversationId: string, adminId: string): Promise<void> {
    const existingParticipant = await manager.findOne(AdminConversationParticipant, {
      where: { conversationId, adminId },
    });

    if (!existingParticipant) {
      const adminParticipant = manager.create(AdminConversationParticipant, {
        conversationId,
        adminId,
        isActive: true,
        lastAccessedAt: new Date(),
      });

      await manager.save(adminParticipant);
      this.logger.log(`Added admin ${adminId} as participant in conversation ${conversationId}`);
    } else if (!existingParticipant.isActive) {
      // Reactivate if inactive
      await manager.update(AdminConversationParticipant,
        { conversationId, adminId },
        { isActive: true, lastAccessedAt: new Date() },
      );
      this.logger.log(`Reactivated admin ${adminId} in conversation ${conversationId}`);
    }
  }

  /**
   * Check if a user is a participant in a conversation (optimized)
   * Handles both regular conversations and admin conversations
   * @param conversation The conversation entity
   * @param userId The user ID to check
   * @param userType Optional user type to avoid additional query
   * @returns True if user is a participant, false otherwise
   */
  private async isUserParticipantInConversation(
    conversation: Conversation,
    userId: string,
    userType?: UserType
  ): Promise<boolean> {
    // Check if user is a direct participant (participant1 or participant2)
    if (conversation.participant1Id === userId || conversation.participant2Id === userId) {
      return true;
    }

    // For admin conversations, check if user is an admin participant
    if (conversation.isAdminConversation) {
      // Get user type if not provided
      let isAdmin = userType === UserType.ADMIN;
      if (userType === undefined) {
        const user = await this.userRepository.findOne({
          where: { id: userId },
          select: ['type'] // Only select the type field for performance
        });
        isAdmin = user?.type === UserType.ADMIN;
      }

      if (isAdmin) {
        // For admins, automatically add them as participants and return true
        // This is more efficient than checking first then adding
        try {
          await this.ensureAdminParticipant(conversation.id, userId);
          return true;
        } catch (error) {
          this.logger.error(`Failed to ensure admin participant: ${error.message}`);
          return false;
        }
      }
    }

    return false;
  }

  /**
   * Optimized user participation validation
   * Combines user type check and participation validation in a single method
   * @param conversation The conversation entity
   * @param userId The user ID to validate
   * @returns True if user is a participant, false otherwise
   */
  private async validateUserParticipation(conversation: Conversation, userId: string): Promise<boolean> {
    // Check if user is a direct participant first (fastest check)
    if (conversation.participant1Id === userId || conversation.participant2Id === userId) {
      return true;
    }

    // For admin conversations, check admin participation
    if (conversation.isAdminConversation) {
      // Get user type in a single query
      const user = await this.userRepository.findOne({
        where: { id: userId },
        select: ['type'] // Only select the type field for performance
      });

      if (user?.type === UserType.ADMIN) {
        // For admins, automatically ensure participation (more efficient than check-then-add)
        try {
          await this.ensureAdminParticipant(conversation.id, userId);
          return true;
        } catch (error) {
          this.logger.error(`Failed to ensure admin participant: ${error.message}`);
          return false;
        }
      }
    }

    return false;
  }
}
